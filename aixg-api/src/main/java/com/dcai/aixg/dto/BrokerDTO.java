package com.dcai.aixg.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.ejuetc.commons.base.valueobj.time.TimeInterval;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.dcai.aixg.domain.broker.Broker")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "经纪人")
public class BrokerDTO extends BaseDTO<BrokerDTO> {

    public BrokerDTO(Long id) {
        super(id);
    }

    @QueryField(value = "id")
    @Schema(description = "brokerId")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long brokerId;

    @QueryField
    @Schema(description = "手机号")
    private String phone;

    @QueryField
    @Schema(description = "克而瑞id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long kerId;

    @QueryField
    @Schema(description = "城市")
    private String cityId;

    @QueryField
    @Schema(description = "城市名称")
    private String cityName;

    @QueryField
    @Schema(description = "名片姓名")
    private String name;

    @QueryField
    @Schema(description = "名片公司名称")
    private String companyName;

    @QueryField
    @Schema(description = "名片简介")
    private String description;

    @QueryField
    @Schema(description = "名片二维码")
    private String qrCode;

    @QueryField
    @Schema(description = "头像")
    private String icon;

    @QueryField
    @Schema(description = "名片有效期")
    private TimeInterval indate;

    @Schema(description = "可用积分")
    private Long point;

    @Schema(description = "名片服务是否当前有效")
    @QueryField
    private Boolean currentlyValid;

    @Schema(description = "公司类型 1 saas公司 2个人店公司")
    private Integer merchantType = 1;

    @Schema(description = "认证状态, 0-未认证，1-已认证")
    private Integer userStatus = 0;

}
