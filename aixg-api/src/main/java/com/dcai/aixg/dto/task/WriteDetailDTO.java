package com.dcai.aixg.dto.task;

import java.util.List;

import com.dcai.aixg.dto.BrokerDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class WriteDetailDTO {

    @Schema(description = "文章ID")
	private String articleId;

    @Schema(description = "writeID")
	private String writeId;

    @Schema(description = "reportId")
	private String reportId;
    
    @Schema(description = "任务状态: ING-执行中, DONE-执行成功, FAIL-执行失败, UNKNOW-未知")
    private String taskStatus;
    
    @Schema(description = "任务状态名称")
    private String taskStatusName = "";
    
    @Schema(description = "文章类型 1:小区专家 2:区域板块专家 3:房源百科专家 4:市场政策解读专家 5:城市置业解读专家 6:二手房估价专家")
    private String writeType;
    
    @Schema(description = "文章类型名称")
    private String writeTypeName = "";
    
//    @Schema(description = "报告类型, COMPARE:优劣势对比, MARKET_POLICY_ANALYSIS:市场政策分析, SUPPORTING_FACILITY_RESEARCH:城市配套深度调研")
//    private String reportType;
//    
//    @Schema(description = "报告类型名称")
//    private String reportTypeName = "";
    
    @Schema(description = "文章标题")
	private String title;
    
    @Schema(description = "文章询问内容")
	private String ask;
    
    @Schema(description = "简短文章正文")
	private String summary;
    
    @Schema(description = "搜索时间")
	private String createdAt;
    
    @Schema(description = "创建时间（前端展示用）")
	private String createdAtShow;
    
    @Schema(description = "文章正文")
	private String content;
    
    @Schema(description = "broker")
	private BrokerDTO broker;
	
    @Schema(description = "房源")
	private List houseInfo;

}
