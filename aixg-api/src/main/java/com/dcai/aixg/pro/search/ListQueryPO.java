package com.dcai.aixg.pro.search;

import java.time.LocalDate;

import com.dcai.aixg.dto.task.TaskDTO;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class ListQueryPO {
	
    @Schema(name = "writeType", description = "类型 1:小区专家 2:区域板块专家 3:房源百科专家 4:市场政策解读专家 5:城市置业解读专家 6:二手房价格评测报告")
    private String writeType;

    //@JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    @Schema(name = "startDate", description = "开始日期")
    private LocalDate startDate;

    //@JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    @Schema(name = "endDate", description = "结束日期")
    private LocalDate endDate;
    
    @Schema(name = "status", description = "状态 ING:生成中 DONE:已生成 FAIL:生成失败")
    private TaskDTO.Status status;
	
    @Schema(name = "keyword", description = "搜索关键字")
    private String keyword;

    @Schema(name = "page", description = "当前页")
    private Integer page = 1;

    @Schema(name = "pageSize", description = "页面条数")
    private Integer pageSize = 10;

}
