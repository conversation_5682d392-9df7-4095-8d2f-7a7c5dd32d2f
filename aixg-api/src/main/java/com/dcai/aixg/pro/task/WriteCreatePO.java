package com.dcai.aixg.pro.task;

import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.ejuetc.commons.base.exception.BusinessException;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class WriteCreatePO {
	
	@NotNull(message = "请输入文章类型")
    @Schema(name = "writeType", description = "文章类型 1:小区专家 2:区域板块专家 3:房源百科专家 4:市场政策解读专家 5:城市置业解读专家 6:二手房估价专家")
    private String writeType;
	
	@NotNull(message = "请输入城市名")
    @Schema(name = "cityName", description = "城市名")
    private String cityName;
	
	//@NotNull(message = "请输入区域名")
    @Schema(name = "regionName", description = "区域名")
    private String regionName;
	
	//@NotNull(message = "请输入板块名")
    @Schema(name = "areaName", description = "板块名")
    private String areaName;
	
	//@NotNull(message = "请输入小区信息")
    @Schema(name = "estate", description = "小区信息")
    private JSONObject estate;
	
	//@NotNull(message = "请输入小区名")
    @Schema(name = "estateName", description = "小区名")
    private String estateName;
	
	//@NotNull(message = "请输入补充信息")
    @Schema(name = "addInfo", description = "补充信息")
    private String addInfo;
	
	//@NotNull(message = "请输入风格")
    @Schema(name = "style", description = "风格")
    private String style = "1"; //默认取值
	
	//@NotNull(message = "请输入政策")
    @Schema(name = "policy", description = "政策")
    private String policy;
	
	//@NotNull(message = "请输入政策时间")
    @Schema(name = "policyTime", description = "政策时间")
    private String policyTime;
	
	//@NotNull(message = "请输入配套")
    @Schema(name = "facilities", description = "配套")
    private List<String> facilities;
	
    @Schema(name = "houseInfo", description = "房源")
	private List<JSONObject> houseInfo;
    
    public void checkParams() {
    	if (StringUtils.isBlank(writeType)) throw new BusinessException("bc.cpm.aixg.1012", "文章类型");
    	if (!(writeType.equals("1") || writeType.equals("2") || writeType.equals("3") || writeType.equals("4") || writeType.equals("5"))) throw new BusinessException("bc.cpm.aixg.1013", "文章类型");
    	if (writeType.equals("1") && StringUtils.isBlank(cityName)) throw new BusinessException("bc.cpm.aixg.1012", "城市");
    	if (writeType.equals("1") && estate == null) throw new BusinessException("bc.cpm.aixg.1012", "小区信息");
    	if (writeType.equals("1") && StringUtils.isBlank(estateName)) throw new BusinessException("bc.cpm.aixg.1012", "小区");
    	if (writeType.equals("2") && StringUtils.isBlank(cityName)) throw new BusinessException("bc.cpm.aixg.1012", "城市");
    	if (writeType.equals("2") && StringUtils.isBlank(regionName)) throw new BusinessException("bc.cpm.aixg.1012", "区域");
    	if (writeType.equals("3") && (houseInfo == null || houseInfo.size() == 0)) throw new BusinessException("bc.cpm.aixg.1012", "房源");
    	if (writeType.equals("4") && StringUtils.isBlank(policy)) throw new BusinessException("bc.cpm.aixg.1012", "政策");
    	if (writeType.equals("5") && StringUtils.isBlank(cityName)) throw new BusinessException("bc.cpm.aixg.1012", "城市");
    	if (writeType.equals("5") && (facilities == null || facilities.size() == 0)) throw new BusinessException("bc.cpm.aixg.1012", "配套");
    }
	  
//	  必填项
//	  userId、writeId
//	  小区专家:cityName、regionName、estateName、style
//	  区域板块专家:cityName、regionName、style
//	  房源百科专家:houseInfo、style
//	  市场政策解读专家:policy、style
//	  城市置业解读专家:cityName、facilities、style

}
