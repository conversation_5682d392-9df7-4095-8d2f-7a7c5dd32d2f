package com.dcai.aixg.impl;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

import java.util.List;

import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.dcai.aixg.api.WriteAPI;
import com.dcai.aixg.domain.broker.Broker;
import com.dcai.aixg.domain.broker.BrokerRpt;
import com.dcai.aixg.domain.task.Report;
import com.dcai.aixg.domain.task.TaskRpt;
import com.dcai.aixg.domain.task.Write;
import com.dcai.aixg.dto.BrokerDTO;
import com.dcai.aixg.dto.task.WriteCreateDTO;
import com.dcai.aixg.dto.task.WriteDetailDTO;
import com.dcai.aixg.dto.task.WriteInfoDTO;
import com.dcai.aixg.integration.wechat.WechatService;
import com.dcai.aixg.kerApi.KerApi;
import com.dcai.aixg.pro.search.ListQueryPO;
import com.dcai.aixg.pro.task.WriteCreatePO;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RefreshScope
@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class WriteImpl implements WriteAPI {

    private final BrokerRpt brokerRpt;
    private final TaskRpt taskRpt;
    private final KerApi kerApi;

	@Override
	public ApiResponse<WriteCreateDTO> doWrite(SaasLoginToken saasLoginToken, @Valid WriteCreatePO po) {
		po.checkParams();
		Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
//		List<Task> taskList4Ing = taskRpt.findAllByTypeAndStatus(broker, TaskDTO.Type.WRITE, TaskDTO.Status.ING);
//		if (taskList4Ing != null && taskList4Ing.size() > 0) throw new BusinessException("bc.cpm.aixg.1008");
		Write write = new Write(broker, po);
		if (StringUtils.isBlank(po.getRegionName()) && po.getEstate() != null) {
			String estateAddress = po.getEstate().getString("district");
			String estateName = po.getEstate().getString("estateExecutionAddress");
			if (StringUtils.isNotBlank(estateAddress) && StringUtils.isNotBlank(estateName)) {
				po.setRegionName(write.getEstateDistrict(estateAddress, estateName));
			}
		}
		WriteCreateDTO dto = kerApi.doWrite(broker.getKerId(), po);
		write.writeSubmit(dto);
		write = taskRpt.save(write);
		if (StringUtils.isBlank(broker.getMessageOpenId())) {
	    	String qrCodeUrl = getBean(WechatService.class).getQrCodeTicket(write.getId());
	    	dto.setQrCodeUrl(qrCodeUrl);
		} else {
			write.setOpenId(broker.getMessageOpenId());
			dto.setFollowStatus(true);
		}
		return succ(dto);
	}

	@Override
	public ApiResponse<List<WriteDetailDTO>> writeList(SaasLoginToken saasLoginToken, @Valid ListQueryPO po) {
		Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
		return kerApi.writeList(broker.getKerId(), po);
	}

	@Override
	public ApiResponse<WriteDetailDTO> writeDetail(SaasLoginToken saasLoginToken, String writeId) {
		Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
		WriteDetailDTO result = kerApi.writeDetail(broker.getKerId(), writeId);
		if (result != null && result.getTaskStatus().equals("DONE")) {
			if (broker.getCurrentlyValid()) {
				result.setBroker(convert2DTO(broker, new BrokerDTO()));
			}
			if (StringUtils.isNotBlank(result.getContent())) {
				List<Write> writeList = taskRpt.findAndLockByWriteId(writeId);
				if (writeList != null && writeList.size() > 0) {
					Write write = writeList.get(0);
					List houseInfo = (write.getHouseInfos() != null && write.getHouseInfos().size() > 0) ? write.getHouseInfos4DelegationVO() : write.getDelegationIds(result.getContent());
					result.setHouseInfo(houseInfo);
				} else {
					result.setHouseInfo(new Write().getDelegationIds(result.getContent()));
				}
			}
		}
		return succ(result);
	}

	@Override
	public ApiResponse<WriteDetailDTO> writeDetail4Share(String writeId) {
        List<Write> writeList = taskRpt.findAndLockByWriteId(writeId);
    	if (writeList == null || writeList.size() == 0) throw new BusinessException("bc.cpm.aixg.1006");
    	Write write = writeList.get(0);
		WriteDetailDTO result = kerApi.writeDetail(write.getBroker().getKerId(), writeId);
		if (result != null && result.getTaskStatus().equals("DONE")) {
			if (write.getBroker().getCurrentlyValid()) {
				result.setBroker(convert2DTO(write.getBroker(), new BrokerDTO()));
			}
			if (StringUtils.isNotBlank(result.getContent())) {
				List houseInfo = (write.getHouseInfos() != null && write.getHouseInfos().size() > 0) ? write.getHouseInfos4DelegationVO() : write.getDelegationIds(result.getContent());
				result.setHouseInfo(houseInfo);
			}
		}
		return succ(result);
	}

	@Override
	public ApiResponse<WriteInfoDTO> writeInfo(SaasLoginToken saasLoginToken) {
		return succ(new WriteInfoDTO());
	}

	@Override
	public ApiResponse<Boolean> deleteWrite(SaasLoginToken saasLoginToken, String writeId) {
		Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
        List<Write> writeList = taskRpt.findAndLockByWriteId(writeId);
    	if (writeList != null && writeList.size() > 0) {
        	Write write = writeList.get(0);
        	if (!broker.getId().equals(write.getBroker().getId())) throw new BusinessException("bc.cpm.aixg.1005");
        	write.setLogicDelete();
        	boolean result = kerApi.writeDelete(broker.getKerId(), writeId);
    		if (!result) return succ(Boolean.FALSE);
    	}
    	return succ(Boolean.TRUE);
	}

	@Override
	public ApiResponse<String> changeFollow(SaasLoginToken saasLoginToken, String writeId) {
        List<Write> writeList = taskRpt.findAndLockByWriteId(writeId);
        if (writeList != null && writeList.size() > 0) {
        	Write write = writeList.get(0);
        	write.setOpenId(null);
        	String qrCodeUrl = getBean(WechatService.class).getQrCodeTicket(write.getId());
        	return succ(qrCodeUrl);
        }
        List<Report> reportList = taskRpt.findAndLockByReportId(writeId);
        if (reportList != null && reportList.size() > 0) {
        	Report report = reportList.get(0);
        	report.setOpenId(null);
        	String qrCodeUrl = getBean(WechatService.class).getQrCodeTicket(report.getId());
        	return succ(qrCodeUrl);
        }
    	throw new BusinessException("bc.cpm.aixg.1006");
	}

}
