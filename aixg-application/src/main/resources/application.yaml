spring:
  profiles:
    active: local
  application:
    business: dcai
    name: aixg
  servlet:
    multipart:
      enabled: true
      max-file-size: 500MB
      max-request-size: 1000MB
  jackson:
    serialization:
      indent_output: true
  jpa:
    properties:
      hibernate:
        default_batch_fetch_size: 100
  mvc:
    hiddenmethod:
      filter:
        enabled: true
    pathmatch:
      matching-strategy: ant_path_matcher
server:
  port: 8020
  servlet:
    context-path: /${spring.application.name}
    session:
      timeout: 1800s
  tomcat:
    uri-encoding: UTF-8
    threads:
      max: 200
    basedir: /tmp
logging:
  config: classpath:logback-spring.xml
knife4j:
  enable: true
  setting:
    language: zh_cn
com:
  ejuetc:
    exception:
      alarm: false
    commons:
      base:
        LoggerFilter:
          ignoreRequestURI: /${spring.application.name}/doc.html*,/${spring.application.name}/webjars/*,/${spring.application.name}/api/healthCheck,/${spring.application.name}/web/search/doSearch,/${spring.application.name}/web/search/doSearch1
management:
  endpoints:
    web:
      exposure:
        include: health

ejuetc:
  commons:
    oss:
      access-key-id: LTAI5tBnSSpJoSdU3kGfcgk6
      access-key-secret: ******************************
      oss-endpoint: oss-cn-shanghai.aliyuncs.com
      url-prefix: https://dcai-oss.ebaas.com/
      bucket-name: dcai-oss
      upload-functions:
        aixg-broker-photo:
          date-format: yyyyMMdd
          max-count: 5
          valid-seconds: 900
  loginFilter:
    enable: true
  login:
    processor:
      saas:
        enable: true
dcai:
  aixg:
    broker:
      propertyUrl: "https://test-api.fangyou.com"
      merchantUrl: "https://test-svc.fangyou.com"
    ker:
      url: "https://prewww.dichanai.com"
      client_id: "test-ejuetc-fb4qikrwmg"
      client_secret: "sk-oA7v9hJvWIy8qxMRTWqyzApOf3bo4kTJ"
    order:
      callBackUrl: "http://dcai-test.ebaas.com/aixg/api/order/orderPayCallBack"
scheduler:
  tasks:
    queryOrder: 0 0/30 * * * ?
wechat:
  appId: wx3798f3fbe7c8972d
  AppSecret: c1242d5fdf5cd29939f6834d27313bd4
  token: aiJvWIy8qxMRTWqyzApdc