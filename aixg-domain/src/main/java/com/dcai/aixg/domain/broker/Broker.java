package com.dcai.aixg.domain.broker;

import com.dcai.aixg.dto.LoginInfoDTO;
import com.dcai.aixg.pro.ApiEditBrokerPO;
import com.dcai.aixg.pro.EditBrokerPO;
import com.dcai.aixg.pro.EditProjectPO;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.valueobj.time.TimeInterval;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.time.LocalDateTime;

import static java.time.LocalDateTime.now;

@Getter
@Entity
@NoArgsConstructor
@Comment("经纪人")
@Table(name = "tb_broker")
@Where(clause = "logic_delete = 0")
public class Broker extends BaseEntity<Broker> {

    @Id
    @Comment("saas user id")
    private Long id;

    @Column(name = "phone", columnDefinition = "varchar(255) COMMENT '手机号'", nullable = false)
    private String phone;

    @Column(name = "ker_id", columnDefinition = "bigint(20) COMMENT '克而瑞id'")
    private Long kerId;

    @Setter
    @Column(name = "city_id", columnDefinition = "varchar(50) COMMENT '城市'")
    private String cityId;

    @Setter
    @Column(name = "city_name", columnDefinition = "varchar(50) COMMENT '城市名称'")
    private String cityName;

    @Column(name = "name", columnDefinition = "varchar(50) COMMENT '名片姓名'")
    private String name;

    @Column(name = "company_name", columnDefinition = "varchar(50) COMMENT '名片公司名称'")
    private String companyName;

    @Column(name = "description", columnDefinition = "varchar(255) COMMENT '名片简介'")
    private String description;

    @Column(name = "qr_code", columnDefinition = "varchar(255) COMMENT '名片二维码'")
    private String qrCode;

    @Column(name = "icon", columnDefinition = "varchar(255) COMMENT '头像'")
    private String icon;

    @Setter
    @Column(name = "open_id", columnDefinition = "varchar(255) COMMENT 'openId'")
    private String openId;

    @Embedded
    private TimeInterval indate;

    @Setter
    @Column(name = "message_open_id", columnDefinition = "varchar(255) COMMENT '公众号发送模板消息openId'")
    private String messageOpenId;

    public Broker(EditProjectPO po) {
        this.id = po.getId();
        edit(po);
    }

    public Broker(LoginInfoDTO loginInfo, Long kerId) {
        this.id = loginInfo.getId();
        this.phone = loginInfo.getPhone();
        this.kerId = kerId;
        this.cityId = loginInfo.getCityId();
        this.cityName = loginInfo.getCityName();
        this.name = loginInfo.getName();
        this.companyName = loginInfo.getCompanyName();
        this.icon = loginInfo.getIcon();
        this.openId = loginInfo.getOpenId();
    }

    public void edit(EditBrokerPO editBrokerPO) {
        this.name = editBrokerPO.getName();
        this.companyName = editBrokerPO.getCompanyName();
        this.description = editBrokerPO.getDescription();
        this.icon = editBrokerPO.getIcon();
        this.qrCode = editBrokerPO.getQrCode();
    }

    public void edit(ApiEditBrokerPO po) {
        if (StringUtils.isNotBlank(po.getName())) {
            this.name = po.getName();
        }
        if (StringUtils.isNotBlank(po.getIcon())) {
            this.icon = po.getIcon();
        }
        this.companyName = po.getCompanyName();
    }

    public void edit(EditProjectPO po) {
        if (po.getPhone() != null) {
            this.phone = po.getPhone();
        }
    }

    public boolean getCurrentlyValid() {
        return indate != null && indate.isInclude(now());
    }

    public void setBusinessCardIndate(Integer expireDays){
        if (this.indate == null || !getCurrentlyValid()) {
            this.indate = new TimeInterval(LocalDateTime.now(), LocalDateTime.now().plusDays(expireDays));
        } else {
            this.indate = new TimeInterval(this.indate.getBeginTime(), this.indate.getEndTime().plusDays(expireDays));
        }
    }
}
