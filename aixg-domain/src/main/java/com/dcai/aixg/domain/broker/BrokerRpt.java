package com.dcai.aixg.domain.broker;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.util.List;

import static com.ejuetc.commons.base.utils.ThreadUtils.redisLock;

public interface BrokerRpt extends JpaRepositoryImplementation<Broker, Long> {

    default Broker findAndLockById(Long brokerId) {
        redisLock(10, 10, "BrokerRpt.findAndLockById", brokerId);
        return findById(brokerId).orElse(null);
    }

    @Query("from Broker where messageOpenId = :messageOpenId")
    List<Broker> findByMessageOpenId(String messageOpenId);

    @Query("from Broker where openId = :openId")
    List<Broker> findByOpenId(String openId);
}
