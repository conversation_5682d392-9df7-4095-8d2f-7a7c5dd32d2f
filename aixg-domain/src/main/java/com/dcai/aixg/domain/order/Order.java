package com.dcai.aixg.domain.order;

import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.domain.broker.Broker;
import com.dcai.aixg.dto.GoodsDTO;
import com.dcai.aixg.dto.OrderDTO;
import com.dcai.aixg.pro.CreateOrderPO;
import com.dcai.aixg.pro.OrderPayCallBackPO;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.usertype.JsonUT;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Entity
@NoArgsConstructor
@Comment("订单")
@Table(name = "tb_order")
@Where(clause = "logic_delete = 0")
public class Order extends BaseEntity<Order> {

    @Id
    @Comment("订单id")
    @GeneratedValue(generator = "order_id")
    @SequenceGenerator(name = "order_id", sequenceName = "seq_order_id")
    private Long id;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "broker_id", columnDefinition = "bigint(20) COMMENT '经纪人ID'")
    private Broker broker;

    @Column(name = "open_id", columnDefinition = "varchar(50) COMMENT '微信openId'", nullable = false)
    private String openId;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, insertable = false, updatable = false)
    private OrderDTO.Type type;

    @Column(name = "goods_id", columnDefinition = "bigint(20) COMMENT '商品id'", nullable = false)
    private Long goodsId;

    @Type(value = JsonUT.class, parameters = {@org.hibernate.annotations.Parameter(name = "type", value = "com.dcai.aixg.dto.GoodsDTO")})
    @Column(name = "goods_json", columnDefinition = "json COMMENT '商品信息'")
    protected GoodsDTO goodsJson;

    @Column(name = "pay_amount", columnDefinition = "decimal(20,2) default 0 COMMENT '付款金额'")
    private BigDecimal payAmount = BigDecimal.ZERO;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, insertable = false, updatable = false)
    private OrderDTO.Status status = OrderDTO.Status.PAY_WAIT;

    @Column(name = "pay_time", columnDefinition = "datetime COMMENT '支付时间'")
    private LocalDateTime payTime;

    @Column(name = "external_order_no", columnDefinition = "varchar(200) COMMENT '微信订单号'")
    private String externalOrderNo;

    @Type(JsonUT.class)
    @Column(name = "ker_resp", columnDefinition = "json COMMENT '创建订单克而瑞应答'")
    private JSONObject kerResp;

    @Column(name = "ker_order_no", columnDefinition = "varchar(50) COMMENT '克而瑞订单编号'")
    private String kerOrderNo;

    public Order(Broker broker, GoodsDTO goodsDTO, CreateOrderPO po) {
        this.broker = broker;
        this.openId = po.getOpenId();
        this.type = po.getGoodsType();
        this.goodsId = goodsDTO.getId();
        this.goodsJson = goodsDTO;
    }

    public void payOrder(JSONObject jsonObject) {
        this.status = OrderDTO.Status.PAYING;
        this.kerResp = jsonObject;
        this.kerOrderNo = jsonObject.getString("orderNo");
    }

    public void payCallBack(OrderPayCallBackPO po) {
        if (status != OrderDTO.Status.PAYING) {
            return;
        }
        this.payAmount = po.getPayAmount();
        this.status = switch (po.getPayStatus()) {
            case 0 -> OrderDTO.Status.PAY_WAIT;
            case 1 -> OrderDTO.Status.PAY_SUCC;
            case 2 -> OrderDTO.Status.PAYING;
            default -> OrderDTO.Status.PAY_FAIL;
        };
        this.payTime = po.getPayTime();
        this.externalOrderNo = po.getExternalOrderNo();
        if (type == OrderDTO.Type.BUSINESS_CARD) {
            broker.setBusinessCardIndate(goodsJson.getExpireDays());
        }
    }
}
