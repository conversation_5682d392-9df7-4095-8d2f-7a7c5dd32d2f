package com.dcai.aixg.domain.task;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.spring.SpringUtil.getProperty;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Parameter;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.domain.broker.Broker;
import com.dcai.aixg.dto.task.TaskDTO;
import com.dcai.aixg.pro.task.CreateTaskCallBackPO;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.usertype.ListUT;
import com.ejuetc.commons.base.utils.StringUtils;
import com.ejuetc.consumer.api.delegation.ApiQueryListPO;
import com.ejuetc.consumer.api.delegation.DelegationAPI;
import com.ejuetc.consumer.web.vo.DelegationVO;
import com.ejuetc.saasapi.sdk.SaaSApiSDK;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorColumn;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Inheritance;
import jakarta.persistence.InheritanceType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

/**
 * 任务
 */
@Entity
@Table(name = "tb_task")
@Comment("任务")
@Where(clause = "logic_delete = 0")
@NoArgsConstructor
@Getter
@Slf4j
@Accessors(chain = true)
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "type", columnDefinition = "enum('REPORT','WRITE') COMMENT '任务类型'")
public abstract class Task extends BaseEntity<Task> {

    @Id
    @Comment("任务id")
    @GeneratedValue(generator = "task_id")
    @SequenceGenerator(name = "task_id", sequenceName = "seq_task_id")
    protected Long id;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "broker_id", columnDefinition = "bigint(20) COMMENT '经纪人ID'")
    protected Broker broker;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, insertable = false, updatable = false)
    protected TaskDTO.Type type;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    protected TaskDTO.Status status = TaskDTO.Status.ING;

    @Column(name = "msg", columnDefinition = "varchar(255) COMMENT '任务信息'")
    protected String msg;

    @Column(name = "open_id", columnDefinition = "varchar(255) COMMENT '消息接收openId'")
    protected String openId;

    @Column(name = "ask", columnDefinition = "varchar(255) COMMENT '话题'")
    protected String ask;

    @Column(name = "topic", columnDefinition = "varchar(255) COMMENT '写作主题'")
    protected String topic;

    @Column(name = "title", columnDefinition = "varchar(255) COMMENT '标题'")
    protected String title;
    
    @Column(name = "summary", columnDefinition = "text COMMENT '简短文章正文'")
    protected String summary;

    @Column(name = "content", columnDefinition = "MEDIUMTEXT COMMENT '内容'")
    protected String content;

    @Type(value = ListUT.class, parameters = {@org.hibernate.annotations.Parameter(name = "splitChar", value = ","), @Parameter(name = "elementType", value = "java.lang.String")})
    @Column(name = "house_ids", columnDefinition = "varchar(255) COMMENT '房源ID列表'")
    protected List<String> houseIds = Collections.emptyList();

    @Type(value = ListUT.class, parameters = @org.hibernate.annotations.Parameter(name = "splitChar", value = "\n"))
    //@Type(value = ListUT.class, parameters = {@Parameter(name = "elementType", value = "com.alibaba.fastjson.JSONObject")})
    @Column(name = "house_infos", columnDefinition = "MEDIUMTEXT COMMENT '房源信息列表'")
    protected List<String> houseInfos = new ArrayList<>();

    @Column(name = "completion_time", columnDefinition = "datetime(6) COMMENT '完成时间'")
    protected LocalDateTime completionTime;
    
    public void handleCallBack4Create(CreateTaskCallBackPO po) {
    	this.status = po.getTaskStatus();
    	this.msg = po.getMsg();
    }
    
    public void setLogicDelete() {
    	this.logicDelete = true;
    }
    
    public void buildTaskDetail(String title, String summary, String content, LocalDateTime completionTime) {
    	this.title = title;
    	this.summary = summary;
    	this.content = content;
    	this.completionTime = completionTime;
    }
    
    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public void handleSendMessageOpenId(String openId) {
        if (StringUtils.notBlank(this.getOpenId())) {
            return;
        }
        this.openId = openId;
        this.broker.setMessageOpenId(openId);
    }

    public void clearingMessageOpenId() {
        this.openId = null;
    }
    
    public List<JSONObject> getHouseInfos4DelegationVO() {
    	if (houseInfos == null || houseInfos.size() == 0) return new ArrayList<>();
    	List<JSONObject> result = houseInfos.stream().map(houseInfo -> {
            try {
            	return JSONObject.parseObject(houseInfo);
//        		ObjectMapper mapper = new ObjectMapper();
//            	return mapper.readValue(houseInfo.toString(), DelegationVO.class);
            } catch (Exception e) {
                return null;
            }
    	}).collect(Collectors.toList());
    	return result;
    }

    public void handleSendMessage() {
        Message message = new Message(this, broker.getMessageOpenId(), broker.getPhone());
        message.handleSendMessage();
        getBean(MessageRpt.class).save(message);
    }
    
    public List<DelegationVO> getDelegationIds(String content) {
    	List<String> delegationIdStrs = new ArrayList<>();
    	// 定义正则表达式模式，用于匹配ID值
        String regex = "\"id\":\\s*(\\d+)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content);
        // 遍历所有匹配项
        while (matcher.find()) {
        	delegationIdStrs.add(matcher.group(1));
        }
        log.info("根据任务内容[{}]查询到的房源ID列表为：{}", content, delegationIdStrs);
        List<Long> delegationIds = delegationIdStrs.stream().map(s -> {
            try {
                return Long.valueOf(s);
            } catch (NumberFormatException e) {
                return null;
            }
        }).collect(Collectors.toList());
        return getHouseInfo(delegationIds);
    }
    
    public List<DelegationVO> getHouseInfo(List<Long> delegationIds) {
    	if (delegationIds == null || delegationIds.size() == 0) return null;
        String saasKeyCode = getProperty("ejuetc.saasApi.keyCode");
        String saasKeySecret = getProperty("ejuetc.saasApi.keySecret");
        String saasHost = getProperty("ejuetc.saasApi.url");
        String consumerUrl = getProperty("ejuetc.consumer.url");
    	SaaSApiSDK sdk = new SaaSApiSDK(saasKeyCode, saasKeySecret, saasHost);
        DelegationAPI delegationAPI = sdk.feignClient(DelegationAPI.class, consumerUrl);
        ApiResponse<List<DelegationVO>> delegation = delegationAPI.query(new ApiQueryListPO().setDelegationIds(delegationIds));
        if (!delegation.isSucc() || delegation.getData() == null) return null;
        return delegation.getData();
    }
}
