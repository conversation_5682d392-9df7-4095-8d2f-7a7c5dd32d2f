package com.dcai.aixg.domain.task;

import static jakarta.persistence.LockModeType.PESSIMISTIC_WRITE;

import java.util.List;

import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import com.dcai.aixg.domain.broker.Broker;
import com.dcai.aixg.dto.task.TaskDTO;

public interface TaskRpt extends JpaRepositoryImplementation<Task, Long> {

    @Lock(PESSIMISTIC_WRITE)
    Task findAndLockById(Long id);
    
    //@Lock(PESSIMISTIC_WRITE)
    @Query("from Task where broker = :broker and type = :type and status = :status")
    List<Task> findAllByTypeAndStatus(Broker broker, TaskDTO.Type type, TaskDTO.Status status);

    @Query("from Write where articleId = ?1")
    @Lock(PESSIMISTIC_WRITE)
    List<Write> findAndLockByArticleId(String articleId);

    @Query("from Write where writeId = ?1")
    @Lock(PESSIMISTIC_WRITE)
    List<Write> findAndLockByWriteId(String writeId);

    @Query("from Report where reportId = ?1")
    @Lock(PESSIMISTIC_WRITE)
    List<Report> findAndLockByReportId(String reportId);

    @Query("from Task where openId = :openId and status = :status")
    List<Task> findByOpenIdAndStatus(String openId, TaskDTO.Status status);

}
